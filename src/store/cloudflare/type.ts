// Cloudflare Store State and Actions
export interface CloudflareStates {
  zones: CloudflareZonesResponse | null;
  zonesApi: CloudflareZonesApiResponse | null;
  loading: boolean;
  error: string | null;
}

export interface CloudflareActions {
  setZones: (zones: CloudflareZonesResponse | null) => void;
  setZonesApi: (zonesApi: CloudflareZonesApiResponse | null) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  fetchCloudflareZones: () => Promise<void>;
  fetchCloudflareZonesApi: (page?: number, per_page?: number) => Promise<void>;
  clearError: () => void;
}

// API Response Types
export interface CloudflareZonesResponse {
  status: boolean;
  message: string;
  data: CloudflareZonesData;
}

export interface CloudflareZonesData {
  summary: CloudflareZonesSummary;
  zones_with_dns_records: CloudflareZone[];
}

export interface CloudflareZonesSummary {
  total_domains: number;
  zones_with_record_counts: Record<string, number>;
}

export interface CloudflareZone {
  zone_id: string;
  zone_name: string;
  zone_status: string;
  account: CloudflareAccount;
  dns_records: CloudflareDnsRecord[];
}

export interface CloudflareAccount {
  id: string;
  name: string;
}

export interface CloudflareDnsRecord {
  id: string;
  name: string;
  type: string;
  content: string;
  proxied: boolean;
}

// New API Response Types for /api/v1/cloudflare/zones/api
export interface CloudflareZonesApiResponse {
  status: boolean;
  message: string;
  data: CloudflareZonesApiData;
}

export interface CloudflareZonesApiData {
  result: CloudflareZoneApi[];
  result_info: CloudflareResultInfo;
  success: boolean;
  errors: any[];
  messages: any[];
}

export interface CloudflareZoneApi {
  id: string;
  name: string;
  status: string;
  paused: boolean;
  type: string;
  development_mode: number;
  name_servers: string[];
  original_name_servers: string[];
  original_registrar: string;
  original_dnshost: string;
  modified_on: string;
  created_on: string;
  activated_on: string;
  meta: CloudflareZoneMeta;
  owner: CloudflareZoneOwner;
  account: CloudflareZoneAccount;
  permissions: string[];
  plan: CloudflareZonePlan;
  always_use_https: string;
}

export interface CloudflareZoneMeta {
  step: number;
  custom_certificate_quota: number;
  page_rule_quota: number;
  phishing_detected: boolean;
  multiple_railguns_allowed: boolean;
}

export interface CloudflareZoneOwner {
  id: string;
  type: string;
  email: string;
}

export interface CloudflareZoneAccount {
  id: string;
  name: string;
}

export interface CloudflareZonePlan {
  id: string;
  name: string;
  price: number;
  currency: string;
  frequency: string;
  is_subscribed: boolean;
  can_subscribe: boolean;
  legacy_id: string;
  legacy_discount: boolean;
  externally_managed: boolean;
}

export interface CloudflareResultInfo {
  page: number;
  per_page: number;
  total_pages: number;
  count: number;
  total_count: number;
}
