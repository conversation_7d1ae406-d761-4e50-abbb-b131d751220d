import { create } from 'zustand';

import {
  getCloudflareZones,
  getCloudflareZonesApi,
} from '@/actions/cloudflare';
import {
  CloudflareActions,
  CloudflareStates,
  CloudflareZonesResponse,
  CloudflareZonesApiResponse,
} from '@/store/cloudflare/type';

export const useCloudflareStore = create<CloudflareStates & CloudflareActions>(
  set => ({
    zones: null,
    zonesApi: null,
    loading: false,
    error: null,

    setZones: (zones: CloudflareZonesResponse | null) => set(() => ({ zones })),
    setZonesApi: (zonesApi: CloudflareZonesApiResponse | null) =>
      set(() => ({ zonesApi })),
    setLoading: (loading: boolean) => set(() => ({ loading })),
    setError: (error: string | null) => set(() => ({ error })),
    clearError: () => set(() => ({ error: null })),

    fetchCloudflareZones: async () => {
      try {
        set(() => ({ loading: true, error: null }));
        const response: any = await getCloudflareZones();

        if (response?.status) {
          set(() => ({
            zones: response,
            loading: false,
            error: null,
          }));
        } else {
          set(() => ({
            zones: null,
            loading: false,
            error: response?.message || 'Failed to fetch Cloudflare zones',
          }));
        }
      } catch (error: any) {
        console.error('Failed to fetch Cloudflare zones:', error);
        set(() => ({
          zones: null,
          loading: false,
          error:
            error?.message ||
            'An error occurred while fetching Cloudflare zones',
        }));
      }
    },

    fetchCloudflareZonesApi: async (
      page: number = 1,
      per_page: number = 10
    ) => {
      try {
        set(() => ({ loading: true, error: null }));
        const response: any = await getCloudflareZonesApi(page, per_page);

        if (response?.status) {
          set(() => ({
            zonesApi: response,
            loading: false,
            error: null,
          }));
        } else {
          set(() => ({
            zonesApi: null,
            loading: false,
            error:
              response?.message || 'Failed to fetch Cloudflare zones from API',
          }));
        }
      } catch (error: any) {
        console.error('Failed to fetch Cloudflare zones from API:', error);
        set(() => ({
          zonesApi: null,
          loading: false,
          error:
            error?.message ||
            'An error occurred while fetching Cloudflare zones from API',
        }));
      }
    },
  })
);
