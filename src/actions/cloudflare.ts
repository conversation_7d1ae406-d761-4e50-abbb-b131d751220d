'use server';

import Http from '@/lib/http';

export async function getCloudflareZones() {
  try {
    const response = await Http.get('/cloudflare/zones');
    return response.data;
  } catch (error) {
    console.log(error);
    throw error;
  }
}

export async function getCloudflareZonesApi(
  page: number = 1,
  per_page: number = 10
) {
  try {
    const response = await Http.get(
      `/cloudflare/zones/api?page=${page}&per_page=${per_page}`
    );
    return response.data;
  } catch (error) {
    console.log(error);
    throw error;
  }
}
