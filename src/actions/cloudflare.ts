'use server';

import Http from '@/lib/http';

export async function getCloudflareZones() {
  try {
    const response = await Http.get('/cloudflare/zones');
    return response.data;
  } catch (error) {
    console.log(error);
    throw error;
  }
}

export async function getCloudflareZonesApi(
  page: number = 1,
  per_page: number = 10,
  name?: string
) {
  try {
    const params = new URLSearchParams({
      page: page.toString(),
      per_page: per_page.toString(),
    });

    // Only add name parameter if it has a non-empty value
    if (name && name.trim()) {
      params.append('name', name.trim());
    }

    const response = await Http.get(`/cloudflare/zones/api?${params.toString()}`);
    return response.data;
  } catch (error) {
    console.log(error);
    throw error;
  }
}
