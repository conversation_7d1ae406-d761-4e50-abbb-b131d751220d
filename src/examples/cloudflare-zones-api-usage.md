# Cloudflare Zones API Usage

This document shows how to use the new Cloudflare Zones API endpoint that supports pagination.

## API Endpoint

```
GET /api/v1/cloudflare/zones/api?page=1&per_page=10
```

## Usage in Components

### Basic Usage

```typescript
import { useCloudflareStore } from '@/store/cloudflare/action';

function CloudflareZonesList() {
  const { zonesApi, loading, error, fetchCloudflareZonesApi } = useCloudflareStore();

  useEffect(() => {
    // Fetch first page with 10 items per page
    fetchCloudflareZonesApi(1, 10);
  }, []);

  if (loading) return <div>Loading...</div>;
  if (error) return <div>Error: {error}</div>;
  if (!zonesApi?.data?.result) return <div>No data</div>;

  return (
    <div>
      <h2>Zones ({zonesApi.data.result_info.total_count} total)</h2>
      {zonesApi.data.result.map(zone => (
        <div key={zone.id}>
          <h3>{zone.name}</h3>
          <p>Status: {zone.status}</p>
          <p>Plan: {zone.plan.name}</p>
        </div>
      ))}
    </div>
  );
}
```

### With Pagination

```typescript
import { useState, useEffect } from 'react';
import { useCloudflareStore } from '@/store/cloudflare/action';

function CloudflareZonesWithPagination() {
  const { zonesApi, loading, error, fetchCloudflareZonesApi } = useCloudflareStore();
  const [currentPage, setCurrentPage] = useState(1);
  const [perPage, setPerPage] = useState(10);

  useEffect(() => {
    fetchCloudflareZonesApi(currentPage, perPage);
  }, [currentPage, perPage]);

  const handlePageChange = (newPage: number) => {
    setCurrentPage(newPage);
  };

  const handlePerPageChange = (newPerPage: number) => {
    setPerPage(newPerPage);
    setCurrentPage(1); // Reset to first page
  };

  if (loading) return <div>Loading...</div>;
  if (error) return <div>Error: {error}</div>;
  if (!zonesApi?.data?.result) return <div>No data</div>;

  const { result, result_info } = zonesApi.data;

  return (
    <div>
      <div>
        <h2>Zones ({result_info.total_count} total)</h2>
        <p>Page {result_info.page} of {result_info.total_pages}</p>
        
        <select value={perPage} onChange={(e) => handlePerPageChange(Number(e.target.value))}>
          <option value={5}>5 per page</option>
          <option value={10}>10 per page</option>
          <option value={20}>20 per page</option>
        </select>
      </div>

      <div>
        {result.map(zone => (
          <div key={zone.id}>
            <h3>{zone.name}</h3>
            <p>Status: {zone.status}</p>
            <p>Plan: {zone.plan.name}</p>
            <p>Account: {zone.account.name}</p>
          </div>
        ))}
      </div>

      <div>
        <button 
          onClick={() => handlePageChange(currentPage - 1)}
          disabled={currentPage <= 1}
        >
          Previous
        </button>
        
        <span>Page {currentPage} of {result_info.total_pages}</span>
        
        <button 
          onClick={() => handlePageChange(currentPage + 1)}
          disabled={currentPage >= result_info.total_pages}
        >
          Next
        </button>
      </div>
    </div>
  );
}
```

## API Response Structure

The API returns data in this format:

```typescript
{
  status: boolean,
  message: string,
  data: {
    result: [
      {
        id: string,
        name: string,
        status: string,
        paused: boolean,
        type: string,
        development_mode: number,
        name_servers: string[],
        original_name_servers: string[],
        original_registrar: string,
        original_dnshost: string,
        modified_on: string,
        created_on: string,
        activated_on: string,
        meta: {
          step: number,
          custom_certificate_quota: number,
          page_rule_quota: number,
          phishing_detected: boolean,
          multiple_railguns_allowed: boolean
        },
        owner: {
          id: string,
          type: string,
          email: string
        },
        account: {
          id: string,
          name: string
        },
        permissions: string[],
        plan: {
          id: string,
          name: string,
          price: number,
          currency: string,
          frequency: string,
          is_subscribed: boolean,
          can_subscribe: boolean,
          legacy_id: string,
          legacy_discount: boolean,
          externally_managed: boolean
        },
        always_use_https: string
      }
    ],
    result_info: {
      page: number,
      per_page: number,
      total_pages: number,
      count: number,
      total_count: number
    },
    success: boolean,
    errors: any[],
    messages: any[]
  }
}
```

## Store Actions

- `fetchCloudflareZonesApi(page?, per_page?)`: Fetch zones with pagination
- `setZonesApi(zonesApi)`: Manually set zones API data
- `setLoading(loading)`: Set loading state
- `setError(error)`: Set error message
- `clearError()`: Clear the current error

## Store State

- `zonesApi`: The complete API response data or null
- `loading`: Boolean indicating if a request is in progress
- `error`: Error message string or null
