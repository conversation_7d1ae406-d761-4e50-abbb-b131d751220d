'use client';

import { useEffect, useState } from 'react';
import { Globe, CheckCircle, XCircle, Loader2, RefreshCw } from 'lucide-react';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useCloudflareStore } from '@/store/cloudflare/action';

export function CloudflareZonesApiExample() {
  const { zonesApi, loading, error, fetchCloudflareZonesApi, clearError } = useCloudflareStore();
  const [currentPage, setCurrentPage] = useState(1);
  const [perPage, setPerPage] = useState(10);

  // Fetch zones when component mounts
  useEffect(() => {
    fetchCloudflareZonesApi(currentPage, perPage);
  }, [currentPage, perPage, fetchCloudflareZonesApi]);

  const handleRetry = () => {
    clearError();
    fetchCloudflareZonesApi(currentPage, perPage);
  };

  const handlePageChange = (newPage: number) => {
    setCurrentPage(newPage);
  };

  const handlePerPageChange = (newPerPage: number) => {
    setPerPage(newPerPage);
    setCurrentPage(1); // Reset to first page when changing per_page
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <Loader2 className="h-8 w-8 animate-spin" />
        <span className="ml-2">Loading Cloudflare zones...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-8 text-center">
        <div className="text-red-600 mb-4">{error}</div>
        <Button onClick={handleRetry} variant="outline">
          <RefreshCw className="h-4 w-4 mr-2" />
          Retry
        </Button>
      </div>
    );
  }

  if (!zonesApi?.data?.result) {
    return (
      <div className="p-8 text-center text-gray-500">
        No zones data available
      </div>
    );
  }

  const { result, result_info } = zonesApi.data;

  return (
    <div className="space-y-6">
      {/* Header with pagination info */}
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold">
          Cloudflare Zones ({result_info.total_count} total)
        </h2>
        <div className="flex items-center gap-4">
          <div className="text-sm text-gray-600">
            Page {result_info.page} of {result_info.total_pages}
          </div>
          <select
            value={perPage}
            onChange={(e) => handlePerPageChange(Number(e.target.value))}
            className="border rounded px-2 py-1 text-sm"
          >
            <option value={5}>5 per page</option>
            <option value={10}>10 per page</option>
            <option value={20}>20 per page</option>
            <option value={50}>50 per page</option>
          </select>
        </div>
      </div>

      {/* Zones List */}
      <div className="grid gap-4">
        {result.map((zone) => (
          <Card key={zone.id} className="hover:shadow-md transition-shadow">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle className="text-base font-medium flex items-center gap-2">
                  <Globe className="h-4 w-4" />
                  <span className="font-mono">{zone.name}</span>
                </CardTitle>
                <div className="flex items-center gap-2">
                  <Badge
                    variant={zone.status === 'active' ? 'default' : 'secondary'}
                  >
                    {zone.status === 'active' ? (
                      <CheckCircle className="h-3 w-3 mr-1" />
                    ) : (
                      <XCircle className="h-3 w-3 mr-1" />
                    )}
                    {zone.status}
                  </Badge>
                  {zone.paused && (
                    <Badge variant="outline">Paused</Badge>
                  )}
                </div>
              </div>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="font-medium">Zone ID:</span>
                  <div className="font-mono text-xs text-gray-600 break-all">
                    {zone.id}
                  </div>
                </div>
                <div>
                  <span className="font-medium">Type:</span>
                  <div className="text-gray-600">{zone.type}</div>
                </div>
                <div>
                  <span className="font-medium">Plan:</span>
                  <div className="text-gray-600">{zone.plan.name}</div>
                </div>
                <div>
                  <span className="font-medium">Account:</span>
                  <div className="text-gray-600 truncate">{zone.account.name}</div>
                </div>
              </div>
              
              <div className="text-sm">
                <span className="font-medium">Name Servers:</span>
                <div className="text-gray-600 text-xs space-y-1 mt-1">
                  {zone.name_servers.map((ns, index) => (
                    <div key={index} className="font-mono">{ns}</div>
                  ))}
                </div>
              </div>

              <div className="flex items-center justify-between text-xs text-gray-500">
                <span>Created: {new Date(zone.created_on).toLocaleDateString()}</span>
                <span>Modified: {new Date(zone.modified_on).toLocaleDateString()}</span>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Pagination */}
      <div className="flex items-center justify-center gap-2">
        <Button
          variant="outline"
          size="sm"
          onClick={() => handlePageChange(currentPage - 1)}
          disabled={currentPage <= 1}
        >
          Previous
        </Button>
        
        <div className="flex items-center gap-1">
          {Array.from({ length: Math.min(5, result_info.total_pages) }, (_, i) => {
            const pageNum = Math.max(1, currentPage - 2) + i;
            if (pageNum > result_info.total_pages) return null;
            
            return (
              <Button
                key={pageNum}
                variant={pageNum === currentPage ? "default" : "outline"}
                size="sm"
                onClick={() => handlePageChange(pageNum)}
              >
                {pageNum}
              </Button>
            );
          })}
        </div>

        <Button
          variant="outline"
          size="sm"
          onClick={() => handlePageChange(currentPage + 1)}
          disabled={currentPage >= result_info.total_pages}
        >
          Next
        </Button>
      </div>

      {/* Summary */}
      <div className="text-center text-sm text-gray-600">
        Showing {result.length} of {result_info.total_count} zones
      </div>
    </div>
  );
}
