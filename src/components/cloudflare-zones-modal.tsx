'use client';

import {
  Cloud,
  Globe,
  CheckCircle,
  XCircle,
  Loader2,
  AlertCircle,
  RefreshCw,
  Plus,
  ChevronLeft,
  ChevronRight,
} from 'lucide-react';
import { useEffect, useState } from 'react';
import { toast } from 'sonner';

import { getDomains } from '@/actions/domain';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  ModalResponsive,
  ModalResponsiveClose,
  ModalResponsiveContent,
  ModalResponsiveDescription,
  ModalResponsiveFooter,
  ModalResponsiveHeader,
  ModalResponsiveTitle,
} from '@/components/ui/modal-responsive';
import { Skeleton } from '@/components/ui/skeleton';
import { useCloudflareStore } from '@/store/cloudflare/action';
import { useDomainStore } from '@/store/domain/action';

interface CloudflareZonesModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  projectId: number;
  onDomainCreated?: () => void;
}

export function CloudflareZonesModal({
  open,
  onOpenChange,
  projectId,
  onDomainCreated,
}: CloudflareZonesModalProps) {
  const { zonesApi, loading, error, fetchCloudflareZonesApi, clearError } =
    useCloudflareStore();
  const { createDomain } = useDomainStore();

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [perPage, setPerPage] = useState(10);

  // Track which zone is being processed for domain creation
  const [creatingDomainForZone, setCreatingDomainForZone] = useState<
    string | null
  >(null);
  const [validatingDomainForZone, setValidatingDomainForZone] = useState<
    string | null
  >(null);

  // Fetch zones when modal opens or pagination changes
  useEffect(() => {
    if (open) {
      fetchCloudflareZonesApi(currentPage, perPage);
    }
  }, [open, currentPage, perPage, fetchCloudflareZonesApi]);

  // Pagination handlers
  const handlePageChange = (newPage: number) => {
    setCurrentPage(newPage);
  };

  const handlePerPageChange = (newPerPage: number) => {
    setPerPage(newPerPage);
    setCurrentPage(1); // Reset to first page when changing per_page
  };

  // Validation function to check if zone can have domain added
  const canAddDomainToZone = (zoneName: string, zoneStatus: string) => {
    const masterDomain = 'irich.info'; // From MASTER_DOMAIN environment variable
    return zoneStatus === 'active' && zoneName !== masterDomain;
  };

  const handleAddDomainToZone = async (zone: any) => {
    if (!canAddDomainToZone(zone.name, zone.status)) {
      return;
    }

    setValidatingDomainForZone(zone.id);

    try {
      // Step 1: Validate that domain doesn't already exist
      const validationResponse: any = await getDomains({
        name: zone.name,
      });

      // Check if domain already exists
      if (
        validationResponse?.status &&
        validationResponse.data &&
        Array.isArray(validationResponse.data) &&
        validationResponse.data.length > 0
      ) {
        toast.error(`Domain '${zone.name}' already exists in this project`);
        return;
      }

      // Step 2: Create domain if validation passes
      setValidatingDomainForZone(null);
      setCreatingDomainForZone(zone.id);

      const domainData = {
        name: zone.name,
        is_default: false,
        is_active: zone.status === 'active',
        zone_id: zone.id,
        account_id: zone.account.id,
        account_name: zone.account.name,
        namespace_id: projectId,
      };

      const response = await createDomain(domainData);

      if (response?.status) {
        toast.success(`Domain "${zone.name}" added successfully`);
        onDomainCreated?.();
        onOpenChange(false);
      } else {
        toast.error(response?.message || 'Failed to add domain');
      }
    } catch (error: any) {
      console.error('Error adding domain:', error);

      // Provide specific error messages
      if (
        error?.message?.includes('already exists') ||
        error?.message?.includes('duplicate')
      ) {
        toast.error(`Domain '${zone.name}' already exists in this project`);
      } else {
        toast.error(error?.message || 'Failed to add domain');
      }
    } finally {
      setValidatingDomainForZone(null);
      setCreatingDomainForZone(null);
    }
  };

  const handleRetry = () => {
    clearError();
    fetchCloudflareZonesApi(currentPage, perPage);
  };

  const handleClose = () => {
    clearError();
    setCreatingDomainForZone(null);
    setValidatingDomainForZone(null);
    onOpenChange(false);
  };

  const ZoneSkeleton = () => (
    <Card>
      <CardHeader className='pb-3'>
        <div className='flex items-center justify-between'>
          <Skeleton className='h-5 w-48' />
          <Skeleton className='h-6 w-16' />
        </div>
      </CardHeader>
      <CardContent className='space-y-2'>
        <div className='flex items-center gap-2'>
          <Skeleton className='h-4 w-4' />
          <Skeleton className='h-4 w-32' />
        </div>
        <div className='flex items-center gap-2'>
          <Skeleton className='h-4 w-4' />
          <Skeleton className='h-4 w-24' />
        </div>
      </CardContent>
    </Card>
  );

  return (
    <>
      <style jsx>{`
        .minimal-scrollbar::-webkit-scrollbar {
          width: 6px;
        }
        .minimal-scrollbar::-webkit-scrollbar-track {
          background: transparent;
        }
        .minimal-scrollbar::-webkit-scrollbar-thumb {
          background: rgba(156, 163, 175, 0.5);
          border-radius: 3px;
        }
        .minimal-scrollbar::-webkit-scrollbar-thumb:hover {
          background: rgba(156, 163, 175, 0.7);
        }
        .dark .minimal-scrollbar::-webkit-scrollbar-thumb {
          background: rgba(75, 85, 99, 0.5);
        }
        .dark .minimal-scrollbar::-webkit-scrollbar-thumb:hover {
          background: rgba(75, 85, 99, 0.7);
        }
      `}</style>
      <ModalResponsive open={open} onOpenChange={onOpenChange}>
        <ModalResponsiveContent className='sm:max-w-4xl max-h-[90vh] flex flex-col'>
          <ModalResponsiveHeader className='flex-shrink-0'>
            <div className='flex items-center gap-3'>
              <div className='flex h-10 w-10 items-center justify-center rounded-full bg-orange-100 dark:bg-orange-900/20'>
                <Cloud className='h-5 w-5 text-orange-600' />
              </div>
              <div>
                <ModalResponsiveTitle>Cloudflare Zones</ModalResponsiveTitle>
                <ModalResponsiveDescription>
                  View available Cloudflare zones and their DNS records count
                </ModalResponsiveDescription>
              </div>
            </div>
          </ModalResponsiveHeader>

          <div
            className='flex-1 overflow-y-auto space-y-6 min-h-0 minimal-scrollbar'
            style={{
              scrollbarWidth: 'thin',
              scrollbarColor: 'rgba(156, 163, 175, 0.5) transparent',
            }}
          >
            {/* Pagination Info and Controls */}
            {zonesApi?.data?.result_info && !loading && !error && (
              <div className='flex items-center justify-between'>
                <div className='flex items-center gap-4'>
                  <div className='text-sm text-muted-foreground'>
                    Showing {zonesApi.data.result.length} of{' '}
                    {zonesApi.data.result_info.total_count} zones
                  </div>
                  <div className='text-sm text-muted-foreground'>
                    Page {zonesApi.data.result_info.page} of{' '}
                    {zonesApi.data.result_info.total_pages}
                  </div>
                </div>
                <div className='flex items-center gap-2'>
                  <select
                    value={perPage}
                    onChange={e => handlePerPageChange(Number(e.target.value))}
                    className='border rounded px-2 py-1 text-sm bg-background'
                  >
                    <option value={5}>5 per page</option>
                    <option value={10}>10 per page</option>
                    <option value={20}>20 per page</option>
                    <option value={50}>50 per page</option>
                  </select>
                </div>
              </div>
            )}

            {/* Loading State */}
            {loading && (
              <div className='space-y-4'>
                <div className='flex items-center justify-center py-8'>
                  <div className='flex items-center gap-2'>
                    <Loader2 className='h-5 w-5 animate-spin' />
                    <span>Loading Cloudflare zones...</span>
                  </div>
                </div>
                <div className='grid gap-4'>
                  {Array.from({ length: 3 }).map((_, index) => (
                    <ZoneSkeleton key={index} />
                  ))}
                </div>
              </div>
            )}

            {/* Error State */}
            {error && (
              <div className='flex flex-col items-center justify-center py-12 space-y-4'>
                <div className='flex h-12 w-12 items-center justify-center rounded-full bg-red-100 dark:bg-red-900/20'>
                  <AlertCircle className='h-6 w-6 text-red-600' />
                </div>
                <div className='text-center'>
                  <h3 className='text-lg font-semibold'>
                    Failed to load zones
                  </h3>
                  <p className='text-muted-foreground mt-1'>{error}</p>
                </div>
                <Button onClick={handleRetry} variant='outline'>
                  <RefreshCw className='h-4 w-4 mr-2' />
                  Try Again
                </Button>
              </div>
            )}

            {/* Zones List */}
            {zonesApi?.data?.result && !loading && !error && (
              <div className='space-y-4'>
                <h3 className='text-lg font-semibold'>Available Zones</h3>
                <div className='grid gap-4'>
                  {zonesApi.data.result.map(zone => (
                    <Card
                      key={zone.id}
                      className='hover:shadow-md transition-shadow'
                    >
                      <CardHeader className='pb-3'>
                        <div className='flex items-center justify-between'>
                          <CardTitle className='text-base font-medium flex items-center gap-2'>
                            <Globe className='h-4 w-4' />
                            <span className='font-mono'>{zone.name}</span>
                          </CardTitle>
                          <div className='flex items-center gap-2'>
                            <Badge
                              variant={
                                zone.status === 'active'
                                  ? 'default'
                                  : 'secondary'
                              }
                            >
                              {zone.status === 'active' ? (
                                <CheckCircle className='h-3 w-3 mr-1' />
                              ) : (
                                <XCircle className='h-3 w-3 mr-1' />
                              )}
                              {zone.status}
                            </Badge>
                            {zone.paused && (
                              <Badge variant='outline'>Paused</Badge>
                            )}
                          </div>
                        </div>
                      </CardHeader>
                      <CardContent className='space-y-3'>
                        <div className='grid grid-cols-2 gap-4 text-sm'>
                          <div>
                            <span className='font-medium'>Zone ID:</span>
                            <div className='font-mono text-xs text-muted-foreground break-all'>
                              {zone.id}
                            </div>
                          </div>
                          <div>
                            <span className='font-medium'>Type:</span>
                            <div className='text-muted-foreground'>
                              {zone.type}
                            </div>
                          </div>
                          <div>
                            <span className='font-medium'>Plan:</span>
                            <div className='text-muted-foreground'>
                              {zone.plan.name}
                            </div>
                          </div>
                          <div>
                            <span className='font-medium'>Account:</span>
                            <div className='text-muted-foreground truncate'>
                              {zone.account.name}
                            </div>
                          </div>
                        </div>

                        <div className='text-sm'>
                          <span className='font-medium'>Name Servers:</span>
                          <div className='text-muted-foreground text-xs space-y-1 mt-1'>
                            {zone.name_servers
                              .slice(0, 2)
                              .map((ns: string, index: number) => (
                                <div key={index} className='font-mono'>
                                  {ns}
                                </div>
                              ))}
                            {zone.name_servers.length > 2 && (
                              <div className='text-xs text-muted-foreground'>
                                +{zone.name_servers.length - 2} more
                              </div>
                            )}
                          </div>
                        </div>

                        <div className='flex items-center justify-between text-xs text-muted-foreground'>
                          <span>
                            Created:{' '}
                            {new Date(zone.created_on).toLocaleDateString()}
                          </span>
                          <span>
                            Modified:{' '}
                            {new Date(zone.modified_on).toLocaleDateString()}
                          </span>
                        </div>

                        {/* Add Domain to Zone Button */}
                        {canAddDomainToZone(zone.name, zone.status) && (
                          <div className='pt-2 border-t'>
                            <Button
                              onClick={() => handleAddDomainToZone(zone)}
                              disabled={
                                creatingDomainForZone === zone.id ||
                                validatingDomainForZone === zone.id
                              }
                              className='w-full'
                              size='sm'
                            >
                              {validatingDomainForZone === zone.id ? (
                                <>
                                  <Loader2 className='h-4 w-4 mr-2 animate-spin' />
                                  Validating Domain...
                                </>
                              ) : creatingDomainForZone === zone.id ? (
                                <>
                                  <Loader2 className='h-4 w-4 mr-2 animate-spin' />
                                  Adding Domain...
                                </>
                              ) : (
                                <>
                                  <Plus className='h-4 w-4 mr-2' />
                                  Add Domain to Zone
                                </>
                              )}
                            </Button>
                          </div>
                        )}

                        {/* Show reason why domain cannot be added */}
                        {!canAddDomainToZone(zone.name, zone.status) && (
                          <div className='pt-2 border-t'>
                            <div className='text-xs text-muted-foreground text-center'>
                              {zone.status !== 'active'
                                ? 'Zone must be active to add domain'
                                : 'Master domain cannot be added'}
                            </div>
                          </div>
                        )}
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </div>
            )}

            {/* Pagination Controls */}
            {zonesApi?.data?.result_info &&
              zonesApi.data.result_info.total_pages > 1 &&
              !loading &&
              !error && (
                <div className='flex items-center justify-center gap-2'>
                  <Button
                    variant='outline'
                    size='sm'
                    onClick={() => handlePageChange(currentPage - 1)}
                    disabled={currentPage <= 1}
                  >
                    <ChevronLeft className='h-4 w-4' />
                    Previous
                  </Button>

                  <div className='flex items-center gap-1'>
                    {Array.from(
                      {
                        length: Math.min(
                          5,
                          zonesApi.data.result_info.total_pages
                        ),
                      },
                      (_, i) => {
                        const pageNum = Math.max(1, currentPage - 2) + i;
                        if (pageNum > zonesApi.data.result_info.total_pages) {
                          return null;
                        }

                        return (
                          <Button
                            key={pageNum}
                            variant={
                              pageNum === currentPage ? 'default' : 'outline'
                            }
                            size='sm'
                            onClick={() => handlePageChange(pageNum)}
                          >
                            {pageNum}
                          </Button>
                        );
                      }
                    )}
                  </div>

                  <Button
                    variant='outline'
                    size='sm'
                    onClick={() => handlePageChange(currentPage + 1)}
                    disabled={
                      currentPage >= zonesApi.data.result_info.total_pages
                    }
                  >
                    Next
                    <ChevronRight className='h-4 w-4' />
                  </Button>
                </div>
              )}

            {/* Empty State */}
            {zonesApi?.data?.result?.length === 0 && !loading && !error && (
              <div className='flex flex-col items-center justify-center py-12 space-y-4'>
                <div className='flex h-12 w-12 items-center justify-center rounded-full bg-muted'>
                  <Cloud className='h-6 w-6 text-muted-foreground' />
                </div>
                <div className='text-center'>
                  <h3 className='text-lg font-semibold'>No zones found</h3>
                  <p className='text-muted-foreground mt-1'>
                    No Cloudflare zones are available at the moment.
                  </p>
                </div>
              </div>
            )}
          </div>

          <ModalResponsiveFooter className='flex-shrink-0'>
            <ModalResponsiveClose asChild>
              <Button variant='outline' onClick={handleClose}>
                Close
              </Button>
            </ModalResponsiveClose>
          </ModalResponsiveFooter>
        </ModalResponsiveContent>
      </ModalResponsive>
    </>
  );
}
